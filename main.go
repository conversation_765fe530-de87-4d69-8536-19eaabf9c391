package main

import (
	"dolphin/internal/handler"
	"dolphin/internal/service"
	"github.com/gin-contrib/sessions"
	"github.com/gin-contrib/sessions/cookie"
	"github.com/gin-gonic/gin"
)

const (
	USERNAME = "lingma"
	PASSWORD = "1234"
)

func main() {

	// 创建gin实例
	router := gin.Default()
	// 设置 session
	store := cookie.NewStore([]byte("secret"))
	router.Use(sessions.Sessions("mysession", store))

	// 修改：正确加载HTML模板文件
	router.LoadHTMLGlob("web/templates/**/*") // 明确指定.html文件后缀
	// 修改路径以匹配静态资源位置
	router.Static("/static", "./web/static")

	// 路由设置
	setupRoutes(router)

	// 启动服务器
	router.Run(":7001")
}

func setupRoutes(router *gin.Engine) {
	// 初始化服务
	logService := service.NewLogService()
	// 启动定时任务
	logService.StartPeriodicLogCheck()
	// 初始化处理器
	authHandler := handler.NewAuthHandler()
	logHandler := handler.NewLogHandler(logService)

	// 页面路由
	router.GET("/", authHandler.LoginPage)
	router.POST("/login", authHandler.Login)

	// 需要认证的路由组
	authorized := router.Group("/")
	authorized.Use(authMiddleware())
	{
		authorized.GET("/log-list", logHandler.LogListPage)
		authorized.GET("/log_analysis", logHandler.LogAnalysisPage)
		authorized.POST("/api/log", logHandler.AnalysisLog)
		authorized.POST("/api/upload", logHandler.UploadLogFile)
		authorized.GET("/api/download", logHandler.DownloadFile)
		authorized.GET("/api/get_log_list", logHandler.GetLogList)
		authorized.POST("/api/update_log_process", logHandler.UpdateLogProcess)
	}
}

func authMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		session := sessions.Default(c)
		user := session.Get("user")
		if user == nil {
			c.Redirect(302, "/")
			c.Abort()
			return
		}
		c.Next()
	}
}
