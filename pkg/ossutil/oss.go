package ossutil

import (
	"bytes"
	"dolphin/pkg/config"
	"encoding/base64"
	"fmt"
	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"io/ioutil"
	"strings"
)

var client *oss.Client

func init() {
	var err error
	accessKeyId := config.GetString("log.oss.accessKeyId")
	accessKeySecret := config.GetString("log.oss.accessKeySecret")
	// 解码 accessKeyId 和 accessKeySecret
	decodedAccessKeyId, err := base64.StdEncoding.DecodeString(accessKeyId)
	ss := string(decodedAccessKeyId)
	print(ss)
	if err != nil {
		panic(fmt.Sprintf("Failed to decode accessKeyId: %v", err))
	}
	decodedAccessKeySecret, err := base64.StdEncoding.DecodeString(accessKeySecret)
	if err != nil {
		panic(fmt.Sprintf("Failed to decode accessKeySecret: %v", err))
	}
	client, err = oss.New(config.GetString("log.oss.endpoint"), string(decodedAccessKeyId), string(decodedAccessKeySecret))
	if err != nil {
		panic(fmt.Sprintf("Failed to initialize OSS client: %v", err))
	}
}

type OssFile struct {
	Name    string
	Content string
}

// ListFiles 列举指定bucket下的文件列表
func ListFiles(bucketName string) ([]string, error) {
	bucket, err := client.Bucket(bucketName)
	if err != nil {
		return nil, fmt.Errorf("failed to get bucket: %v", err)
	}

	var files []string
	marker := ""
	for {
		lsRes, err := bucket.ListObjects(oss.Marker(marker))
		if err != nil {
			return nil, fmt.Errorf("failed to list objects: %v", err)
		}
		for _, object := range lsRes.Objects {
			// 只添加以 "cloud/" 开头且不等于 "cloud" 的文件
			if object.Key != "cloud/" && strings.HasPrefix(object.Key, "cloud/") {
				files = append(files, object.Key)
			}
		}
		if !lsRes.IsTruncated {
			break
		}
		marker = lsRes.NextMarker
	}
	return files, nil
}

// ReadFileContent 根据指定文件名读取文件内容
func ReadFileContent(bucketName, fileName string) ([]byte, error) {
	bucket, err := client.Bucket(bucketName)
	if err != nil {
		return []byte(""), fmt.Errorf("failed to get bucket: %v", err)
	}

	body, err := bucket.GetObject(fileName)
	if err != nil {
		return []byte(""), fmt.Errorf("failed to get object: %v", err)
	}
	defer body.Close()

	data, err := ioutil.ReadAll(body)
	if err != nil {
		return []byte(""), fmt.Errorf("failed to read object content: %v", err)
	}

	return data, nil
}

// WriteFileContent 根据指定文件名写入文件内容
func WriteFileContent(bucketName, fileName, content string) error {
	bucket, err := client.Bucket(bucketName)
	if err != nil {
		return fmt.Errorf("failed to get bucket: %v", err)
	}

	err = bucket.PutObject(fileName, bytes.NewBufferString(content))
	if err != nil {
		return fmt.Errorf("failed to put object: %v", err)
	}

	return nil
}
