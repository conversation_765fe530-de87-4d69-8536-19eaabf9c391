package httpclient

import (
	"bytes"
	"dolphin/pkg/config"
	"encoding/json"
	"fmt"
	"github.com/golang-jwt/jwt/v4"
	"io/ioutil"
	"net/http"
)

// HttpClient 是一个发送 HTTP 请求的工具类
type HttpClient struct {
	client *http.Client
}

// NewHttpClient 创建并返回一个新的 HttpClient 实例
func NewHttpClient() *HttpClient {
	return &HttpClient{
		client: &http.Client{},
	}
}

func (h *HttpClient) Get(url string) ([]byte, error) {
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create GET request: %v", err)
	}
	addSignatureAndEnvCookie(req)

	resp, err := h.client.Do(req)
	if err != nil {
		return nil, fmt.E<PERSON>rf("failed to execute GET request: %v", err)
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read GET response body: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d, body: %s", resp.StatusCode, string(body))
	}

	return body, nil
}

// Post 发送 POST 请求
func (h *HttpClient) Post(url string, body interface{}) ([]byte, error) {
	var buf bytes.Buffer
	if body != nil {
		jsonBody, err := json.Marshal(body)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal POST request body: %v", err)
		}
		buf = *bytes.NewBuffer(jsonBody)
	}

	req, err := http.NewRequest("POST", url, &buf)
	if err != nil {
		return nil, fmt.Errorf("failed to create POST request: %v", err)
	}

	addSignatureAndEnvCookie(req)

	if body != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	resp, err := h.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to execute POST request: %v", err)
	}
	defer resp.Body.Close()

	respBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read POST response body: %v", err)
	}

	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusCreated {
		return nil, fmt.Errorf("unexpected status code: %d, body: %s", resp.StatusCode, string(respBody))
	}

	return respBody, nil
}

// GenerateToken 生成JWT签名
func generateToken() string {
	// 硬编码的密钥（与Java中的JWT_SECRET保持一致）
	secret := config.GetString("lingma-server.auth.jwt-secret")
	appId := config.GetString("lingma-server.auth.appId")
	// 创建JWT声明
	claims := jwt.MapClaims{
		"appId": appId,
	}

	// 创建并签名JWT
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	ss, err := token.SignedString([]byte(secret))
	if err != nil {
		return ""
	}
	return ss
}

func addSignatureAndEnvCookie(req *http.Request) {
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", generateToken()))
	req.Header.Set("X-Internal-Source", "diagnoseLog-analysis-tool")
	env := config.GetString("lingma-server.env")
	if env == "daily" {
		req.AddCookie(&http.Cookie{Name: "version", Value: "ga_dev"})
	} else if env == "pre" {
		req.AddCookie(&http.Cookie{Name: "version", Value: "ga"})
	}
}
