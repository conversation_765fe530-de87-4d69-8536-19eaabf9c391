package config

import (
	"github.com/spf13/viper"
	"strings"
)

func init() {
	viper.SetConfigFile("configs/config.yaml")
	viper.AutomaticEnv() // 环境变量覆盖
	viper.SetEnvPrefix("APP")
	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_")) // 重要！
	if err := viper.ReadInConfig(); err != nil {
		panic(err)
	}
}

// 获取任意层级的配置（带类型安全）
func GetString(path string) string {
	return viper.GetString(path)
}

func GetInt(path string) int {
	return viper.GetInt(path)
}

func GetBool(path string) bool {
	return viper.GetBool(path)
}

// GetSubMap 获取嵌套对象（如需要）
func GetSubMap(path string) map[string]interface{} {
	return viper.GetStringMap(path)
}

// GetJson 获取JSON类型的配置
func GetJson(path string) interface{} {
	return viper.Get(path)
}

// GetObject 将配置值赋值给传入的结构体对象
func GetObject(path string, configStruct interface{}) {
	err := viper.UnmarshalKey(path, configStruct)
	if err != nil {
		panic(err)
	}
}
