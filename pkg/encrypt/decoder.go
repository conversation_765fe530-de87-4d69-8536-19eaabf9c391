package encrypt

import (
	"fmt"
	"log"
	"os"
	"strconv"
	"strings"
	"time"
)

func Decode(logPath string) []string {
	// 读取文件
	content, err := os.ReadFile(logPath)
	if err != nil {
		log.Fatal(err)
	}
	var processedLines []string
	var usingEncodeVersion = detectVersion(string(content))

	if usingEncodeVersion == "1" {
		processedLines = processV1(string(content))
	} else {
		processedLines = processV2(string(content))
	}
	return processedLines
}

func detectVersion(content string) string {
	lines := strings.Split(string(content), ";")
	if len(lines) < 10 {
		//老版用;分隔
		return "2"
	}
	return "1"
}

func processV1(content string) []string {
	// 按行处理文件内容
	lines := strings.Split(string(content), ";")
	var processedLines []string
	for _, line := range lines {
		// 处理每一行内容
		line = processLine(line)
		if line != "" {
			parts := strings.SplitN(line, " ", 2)
			if len(parts) == 2 {
				timestamp, err := strconv.ParseInt(parts[0], 10, 64)
				if err == nil {
					formattedTime := time.UnixMilli(timestamp).Format("2006-01-02 15:04:05.000")
					processedLines = append(processedLines, fmt.Sprintf("%s %s", formattedTime, parts[1]))
				}
			}
		}
	}
	return processedLines
}

func processV2(content string) []string {
	// 按行处理文件内容
	lines := strings.Split(string(content), "\n")
	var processedLines []string
	for _, line := range lines {
		// 处理每一行内容
		line = processLine(line)
		line = strings.TrimRight(line, " \n\r")
		if line != "" {
			processedLines = append(processedLines, line)
		}
	}
	return processedLines
}

func processLine(line string) string {
	processedLine, _ := customDecryptV1([]byte(line))
	return string(processedLine)
}
