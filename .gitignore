# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
*.zip
*.prof

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Mac file
.DS_Store

# Project file
.idea/*
.theia/*
.vscode/*
*.iml

note/
out/

# ignore the vendor directory in the project root
/vendor/

cosy
DEV_VERSION

*.actual.*_chunk.yaml
*.actual.*_chunk.yml
go.sum