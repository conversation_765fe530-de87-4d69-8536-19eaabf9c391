body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f5f5f5;
}

.main-container {
    max-width: 90%;
    margin: 0 auto;
    padding: 20px;
}

.login-container {
    width: 300px;
    margin: 100px auto;
    padding: 20px;
    background-color: white;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.form-group {
    margin-bottom: 15px;
}

.form-group input {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-sizing: border-box;
}

button {
    width: 100%;
    padding: 10px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
}

button:hover {
    background-color: #45a049;
}

.error-message {
    color: red;
    margin-top: 10px;
    text-align: center;
}

.log-analysis-container {
    background-color: white;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    width: 100%;
}

/* 日志列表容器样式 */
.log-list-container {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    width: 100%;
}

/* 过滤输入框样式 */
.filter-input {
    flex: 1;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.08);
    transition: all 0.2s ease;
}

/* 表格头部样式 */
.log-table-header {
    padding: 12px;
    border: 1px solid #ddd;
    font-weight: bold;
    text-align: center;
    background-color: #f5f5f5;
}

/* 表格整体样式 */
#logTable {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
    max-width: 100%;
}

/* 搜索和过滤区域的共享样式 */
.search-section, .filter-section {
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 12px;
    background-color: #f5f5f5;
    padding: 15px;
    border-radius: 8px;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 搜索区域特定样式 */
.search-section {
    margin-bottom: 15px;
}

/* 输入框共享样式 */
.search-section input, .filter-section input {
    flex: 1;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.08);
    transition: all 0.2s ease;
}

.search-section input:focus, .filter-section input:focus {
    outline: none;
    border-color: #4CAF50;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
}

/* 搜索按钮样式 */
.search-section button {
    width: auto;
    min-width: 100px;
    padding: 10px 18px;
    font-weight: bold;
    border-radius: 6px;
    transition: all 0.2s ease;
    background-color: #4CAF50;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-section button:hover {
    background-color: #45a049;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
}

.search-section button:active {
    transform: translateY(1px);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 上传本地日志按钮样式 */
.upload-label {
    width: auto;
    min-width: 100px;
    padding: 10px 18px;
    font-weight: bold;
    border-radius: 6px;
    transition: all 0.2s ease;
    background-color: #4CAF50;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    color: white;
    cursor: pointer;
    text-align: center;
    display: inline-block;
    font-size: 13px;
}

.upload-label:hover {
    background-color: #45a049;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
}

.upload-label:active {
    transform: translateY(1px);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 过滤状态文本样式 */
#filterStatus {
    color: #666;
    font-size: 0.9em;
    background-color: #fff;
    padding: 5px 10px;
    border-radius: 4px;
    min-width: 120px;
    text-align: center;
}

.log-content {
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 4px;
    min-height: 200px;
    background-color: #f9f9f9;
    overflow-x: auto;
}

/* 日志表格样式 */
.log-table {
    width: 100%;
    border-collapse: collapse;
    table-layout: auto;
}

.log-table th, .log-table td {
    padding: 12px;
    border: 1px solid #ddd;
    text-align: left;
}

.log-table th {
    background-color: #f5f5f5;
    font-weight: bold;
    text-align: center;
}

.log-table tr:nth-child(even) {
    background-color: #f9f9f9;
}

.log-table tr:hover {
    background-color: #f0f9ff;
}

/* 调整第一列数字居中并减小列宽 */
.log-table td:first-child {
    text-align: center;
    width: 50px;
    min-width: 50px;
}

/* 查看日志详情按钮样式 */
.log-table button {
    padding: 6px 12px; /* 调整内边距 */
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px; /* 调整字体大小 */
    transition: all 0.2s ease;
    width: auto; /* 新增：根据内容自适应宽度 */
    white-space: nowrap; /* 新增：防止文本换行 */
}

.log-table button:hover:not([disabled]) {
    background-color: #45a049;
}

/* 分页控件样式 */
.pagination {
    margin: 15px 0;
    display: flex;
    align-items: center;
    background-color: #f5f5f5;
    padding: 10px 15px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-left: auto; /* 修改：靠右对齐 */
}

.page-info {
    color: #666;
    font-size: 0.9em;
}

.page-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.page-controls button {
    width: auto;
    padding: 10px 18px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.page-controls button:hover:not([disabled]) {
    background-color: #45a049;
}

.page-controls button[disabled] {
    background-color: #cccccc;
    cursor: not-allowed;
    opacity: 0.7;
}

.page-number {
    color: #666;
    font-weight: bold;
    min-width: 80px;
    text-align: center;
}

/* 日志类型选择器样式 */
.log-type-select {
    min-width: 120px;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    background-color: white;
    font-size: 14px;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.08);
    transition: all 0.2s ease;
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 10px center;
    background-size: 16px;
    cursor: pointer;
}

.log-type-select:focus {
    outline: none;
    border-color: #4CAF50;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
}