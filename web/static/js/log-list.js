document.addEventListener("DOMContentLoaded", function () {
    const tableBody = document.querySelector("#logTable tbody");
    const filterInput = document.getElementById("filterInput");

    // 加载日志列表数据
    function loadLogList(filter = "") {
        fetch(`/api/get_log_list?filter=${encodeURIComponent(filter)}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    renderLogList(data.logList);
                } else {
                    console.error("加载日志列表失败");
                }
            });
    }

    // 渲染日志列表
    function renderLogList(logList) {
        tableBody.innerHTML = ""; // 清空表格内容
        logList.forEach((log, index) => {
            const row = document.createElement("tr");
            row.innerHTML = `
                <td>${log.id}</td>
                <td>${log.userId}</td>
                <td>${log.userName}</td>
                <td>${log.description}</td>
                <td>${log.plugVersion}</td>
                <td>${log.ideType}</td>
                <td>${log.attachmentPath}</td>
                <td>${log.gmtCreate}</td>
                <td>
                    <select onchange="updateLogType('${log.id}', this.value)" style="padding: 5px 8px; border: 1px solid #ddd; border-radius: 4px;">
                        <option value=""${!log.type ? ' selected' : ''}>请选择</option>
                        <option value="性能"${log.type === '性能' ? ' selected' : ''}>性能</option>
                        <option value="交互"${log.type === '交互' ? ' selected' : ''}>交互</option>
                        <option value="效果"${log.type === '效果' ? ' selected' : ''}>效果</option>
                    </select>
                </td>
                <td>${log.processState}</td>
                <td>${log.processResult}</td>
                <td><button onclick="viewLog('${log.ossLink}')">查看日志详情</button></td>
            `;
            tableBody.appendChild(row);
        });
    }

    // 查看日志详情
    window.viewLog = function (ossLink) {
        window.location.href = `/log_analysis?ossLink=${encodeURIComponent(ossLink)}`;
    };

    // 监听输入框变化
    filterInput.addEventListener("input", function () {
        loadLogList(filterInput.value);
    });

    // 初始化加载
    loadLogList();
});