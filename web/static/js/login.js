document.getElementById('loginForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    const formData = new FormData(e.target);

    try {
        const response = await fetch('/login', {
            method: 'POST',
            body: formData
        });
        const result = await response.json();

        if (result.success) {
            window.location.href = result.redirect;
        } else {
            document.getElementById('errorMessage').textContent = result.message;
        }
    } catch (error) {
        document.getElementById('errorMessage').textContent = '登录失败，请重试';
    }
});