// 存储原始日志数据
let originalLogLines = [];
// 当前页码和每页行数
let currentPage = 1;
const rowsPerPage = 1000;

document.getElementById('searchButton').addEventListener('click', async () => {
    const logId = document.getElementById('logId').value;
    const logType = document.getElementById('logType').value;
    
    if (!logId) {
        alert('请输入日志ID');
        return;
    }

    try {
        const response = await fetch('/api/log', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `logId=${encodeURIComponent(logId)}&logType=${encodeURIComponent(logType)}`
        });
        const result = await response.json();
        if (result.success) {
            // 保存原始日志数据
            originalLogLines = result.logLines || [];
            // 重置页码
            currentPage = 1;
            
            // 渲染日志数据（第一页）
            renderLogTablePaginated(originalLogLines);
            
            // 清空过滤框
            document.getElementById('filterKeyword').value = '';
            // 显示总记录数
            document.getElementById('filterStatus').textContent = `共 ${originalLogLines.length} 条记录`;
            document.getElementById('filePathInfo').textContent = `临时文件路径: ${result.filePath}`;
            document.getElementById('downloadButton').setAttribute('data-filepath', result.filePath); // 设置文件路径属性
        } else {
            document.getElementById('logContent').innerHTML = '<p>获取日志失败</p>';
        }
    } catch (error) {
        document.getElementById('logContent').innerHTML = '<p>系统错误，请重试</p>';
    }
});

// 新增文件上传功能
document.getElementById('logFileUpload').addEventListener('change', function(event) {
    const file = event.target.files[0];
    if (file) {
        const formData = new FormData();
        formData.append('logFile', file);

        fetch('/api/upload', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                originalLogLines = result.logLines || [];
                currentPage = 1;
                renderLogTablePaginated(originalLogLines);
                document.getElementById('filterKeyword').value = '';
                document.getElementById('filterStatus').textContent = `共 ${originalLogLines.length} 条记录`;
                // 显示文件路径
                document.getElementById('filePathInfo').textContent = `临时文件路径: ${result.filePath}`;
                document.getElementById('downloadButton').setAttribute('data-filepath', result.filePath); // 设置文件路径属性
            } else {
                alert('上传失败: ' + result.message);
            }
        })
        .catch(error => {
            alert('系统错误，请重试');
        });
    }
});

// 监听过滤输入框的输入事件
document.getElementById('filterKeyword').addEventListener('input', function() {
    const keyword = this.value.trim();
    
    // 如果原始数据为空，提示用户先查询日志
    if (originalLogLines.length === 0) {
        document.getElementById('filterStatus').textContent = '请先查询日志';
        return;
    }
    
    // 重置页码
    currentPage = 1;
    
    // 过滤日志数据
    if (keyword === '') {
        // 如果关键词为空，显示全部日志，并显示总行数
        renderLogTablePaginated(originalLogLines);
        document.getElementById('filterStatus').textContent = `共 ${originalLogLines.length} 条记录`;
    } else {
        // 过滤包含关键词的行
        const filteredLines = originalLogLines.filter(line => 
            line.toLowerCase().includes(keyword.toLowerCase())
        );
        
        renderLogTablePaginated(filteredLines);
        document.getElementById('filterStatus').textContent = 
            `显示 ${filteredLines.length}/${originalLogLines.length} 条记录`;
    }
});

// 分页渲染日志表格的函数
function renderLogTablePaginated(logLines) {
    const logContentDiv = document.getElementById('logContent');
    
    if (logLines && logLines.length > 0) {
        // 计算总页数
        const totalPages = Math.ceil(logLines.length / rowsPerPage);
        // 确保当前页码有效
        currentPage = Math.max(1, Math.min(currentPage, totalPages));
        
        // 计算当前页的起始和结束索引
        const startIndex = (currentPage - 1) * rowsPerPage;
        const endIndex = Math.min(startIndex + rowsPerPage, logLines.length);
        
        // 提取当前页数据
        const currentPageData = logLines.slice(startIndex, endIndex);
        
        // 渲染表格
        let tableHtml = '<table id="logTable" class="log-table"><tbody>';
        currentPageData.forEach((line, index) => {
            // 显示全局行号（从1开始）
            const globalIndex = startIndex + index + 1;
            tableHtml += `<tr>
                <td class="line-number">${globalIndex}</td>
                <td class="log-line">${line}</td>
            </tr>`;
        });
        tableHtml += '</tbody></table>';
        
        // 添加分页控件
        let paginationHtml = '<div class="pagination">';
        paginationHtml += `<div class="page-info">显示第 ${startIndex + 1} - ${endIndex} 行，共 ${logLines.length} 行</div>`;
        paginationHtml += '<div class="page-controls">';
        
        // 上一页按钮
        paginationHtml += `<button id="prevPage" ${currentPage === 1 ? 'disabled' : ''}>上一页</button>`;
        
        // 页码信息
        paginationHtml += `<span class="page-number">第 ${currentPage}/${totalPages} 页</span>`;
        
        // 下一页按钮
        paginationHtml += `<button id="nextPage" ${currentPage === totalPages ? 'disabled' : ''}>下一页</button>`;
        
        paginationHtml += '</div></div>';
        
        // 更新DOM
        logContentDiv.innerHTML = paginationHtml + tableHtml;
        
        // 添加分页按钮事件监听
        setupPaginationListeners(logLines);
        
    } else {
        logContentDiv.innerHTML = '<p>没有找到相关日志数据</p>';
    }
}

// 设置分页按钮事件监听
function setupPaginationListeners(logLines) {
    const prevPageBtn = document.getElementById('prevPage');
    const nextPageBtn = document.getElementById('nextPage');
    
    if (prevPageBtn) {
        prevPageBtn.addEventListener('click', () => {
            if (currentPage > 1) {
                currentPage--;
                renderLogTablePaginated(logLines);
            }
        });
    }
    
    if (nextPageBtn) {
        nextPageBtn.addEventListener('click', () => {
            const totalPages = Math.ceil(logLines.length / rowsPerPage);
            if (currentPage < totalPages) {
                currentPage++;
                renderLogTablePaginated(logLines);
            }
        });
    }
}

// 新增下载按钮点击事件
document.getElementById('downloadButton').addEventListener('click', function() {
    const filePath = this.getAttribute('data-filepath');
    if (filePath) {
        window.location.href = `/api/download?filePath=${encodeURIComponent(filePath)}`;
    } else {
        alert('文件路径无效');
    }
});