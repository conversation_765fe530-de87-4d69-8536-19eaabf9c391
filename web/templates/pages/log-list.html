<!DOCTYPE html>
<html lang="utf-8">
<head>
    <title>日志列表</title>
    <link rel="stylesheet" href="/static/css/style.css?sksk">
    <!-- 引入flatpickr的CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <!-- 引入扁平化主题 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/themes/material_blue.css">
    <style>
        /* 表格整体样式 */
        #logTable {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
            max-width: 100%;
        }

        /* 单元格样式优化 */
        #logTable td {
            padding: 14px;
            text-align: left;
            border: 1px solid #e0e0e0;
            vertical-align: middle;
            word-break: break-word;
        }

        /* 表格头部样式优化 */
        .log-table-header {
            background-color: #454545;
            color: #ffffff;
            font-size: 16px;
            font-weight: bold;
            text-align: center;
            padding: 16px;
            border: none;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
            word-break: break-word;
        }

        /* 操作列样式 */
        .operation-column {
            width: 200px; /* 设置操作列的固定宽度 */
        }

        /* 操作列单元格样式 */
        #logTable td:last-child {
            text-align: center; /* 设置操作列内容居中 */
            width: 200px; /* 确保与表头宽度一致 */
        }
        
        /* 操作按钮容器样式 */
        .operation-buttons {
            display: flex;
            flex-wrap: wrap;
            justify-content: center; /* 按钮居中 */
            gap: 8px; /* 按钮之间的间距 */
        }

        /* 优化：过滤条件区块样式 */
        .filter-container {
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            padding: 24px;
            background-color: #ffffff;
            margin-bottom: 28px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
            width: 100%; /* 确保宽度与表格一致 */
            box-sizing: border-box; /* 确保padding和border不会增加元素宽度 */
        }

        .filter-row {
            display: flex;
            flex-wrap: wrap; /* 增加响应式支持 */
            gap: 18px;
            margin-bottom: 18px;
            align-items: center; /* 确保垂直对齐 */
        }

        .filter-row:last-child {
            margin-bottom: 0;
        }

        .filter-input {
            flex: 1;
            min-width: 180px; /* 设置最小宽度，防止在小屏幕上过窄 */
            padding: 12px 14px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.2s ease;
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .filter-input:focus {
            border-color: #4CAF50;
            box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
            outline: none;
        }

        .filter-select {
            appearance: none;
            background: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23666666'%3e%3cpath d='M7 10l5 5 5-5z'/%3e%3c/svg%3e") no-repeat right 12px center/12px;
            padding-right: 36px;
        }

        /* flatpickr样式定制 */
        .date-input {
            cursor: pointer; 
            background: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='%23666666'%3e%3cpath d='M9 11H7v2h2v-2zm4 0h-2v2h2v-2zm4 0h-2v2h2v-2zm2-7h-1V2h-2v2H8V2H6v2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 16H5V9h14v11z'/%3e%3c/svg%3e") no-repeat right 12px center/18px;
            padding-right: 40px !important;
        }

        /* 定制flatpickr的样式 */
        .flatpickr-calendar {
            border-radius: 8px;
            box-shadow: 0 3px 15px rgba(0, 0, 0, 0.15);
            margin-top: 5px;
        }

        .flatpickr-time input:hover,
        .flatpickr-time .flatpickr-am-pm:hover,
        .flatpickr-time input:focus,
        .flatpickr-time .flatpickr-am-pm:focus {
            background: #f5f5f5;
        }

        .filter-buttons {
            display: flex;
            gap: 12px;
            margin-left: auto; /* 使按钮容器靠右对齐 */
        }
        
        .buttons-row {
            justify-content: flex-end; /* 确保整行靠右对齐 */
        }

        #queryButton {
            padding: 12px 28px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 15px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            min-width: 120px; /* 确保按钮有足够宽度 */
        }

        #resetButton {
            padding: 12px 28px;
            background-color: #f5f5f5;
            color: #555;
            border: 1px solid #ddd;
            border-radius: 8px;
            cursor: pointer;
            font-size: 15px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            min-width: 120px; /* 确保按钮有足够宽度 */
        }

        #queryButton:hover, #resetButton:hover {
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
            transform: translateY(-1px);
        }

        #queryButton:hover {
            background-color: #45a049;
        }

        #resetButton:hover {
            background-color: #e8e8e8;
        }

        #queryButton:active, #resetButton:active {
            transform: translateY(1px);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        /* 操作列按钮样式优化 */
        #logTable td button {
            padding: 8px 14px;
            font-size: 13px;
            width: auto;
            white-space: nowrap;
            margin-right: 0; /* 移除右侧外边距，使用gap控制间距 */
            margin-bottom: 0; /* 移除底部外边距，使用gap控制间距 */
            border-radius: 6px;
            border: none;
            background-color: #f0f0f0;
            color: #333;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        #logTable td button:hover {
            background-color: #e0e0e0;
        }

        /* 处理状态样式 */
        .status-pill {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-weight: 500;
            font-size: 14px;
            text-align: center;
            min-width: 80px;
        }
        
        .status-processed {
            background-color: #e8f5e9;
            color: #2e7d32;
            border: 1px solid #a5d6a7;
        }
        
        .status-pending {
            background-color: #ffebee;
            color: #c62828;
            border: 1px solid #ef9a9a;
        }

        /* 分页控件样式优化 */
        .pagination {
            margin: 24px 0;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            background-color: #f9f9f9;
            padding: 14px 18px;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }

        .page-info {
            color: #555;
            font-size: 14px;
            margin-right: 20px;
        }

        .page-controls button {
            padding: 8px 18px;
            font-size: 14px;
            border: none;
            border-radius: 6px;
            background-color: #4CAF50;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .page-controls button:hover:not([disabled]) {
            background-color: #45a049;
            box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
            transform: translateY(-1px);
        }

        .page-controls button:active:not([disabled]) {
            transform: translateY(1px);
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .page-controls button[disabled] {
            background-color: #ccc;
            color: #888;
            cursor: not-allowed;
            box-shadow: none;
            opacity: 0.7;
        }

        .page-number {
            font-size: 14px;
            color: #333;
            font-weight: bold;
            margin: 0 14px;
        }

        /* 优化：弹框样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.6);
            z-index: 1000;
            justify-content: center;
            align-items: center;
            backdrop-filter: blur(3px); /* 添加背景模糊效果 */
        }

        .modal-content {
            background-color: #fff;
            padding: 32px;
            border-radius: 12px;
            width: 500px;
            max-width: 85%;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center; /* 确保内容整体居中 */
        }

        .modal-header {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 28px;
            padding-bottom: 16px;
            border-bottom: 1px solid #eee;
            position: relative;
            width: 100%; /* 确保标题栏占满宽度 */
        }

        .modal-title {
            font-size: 22px;
            font-weight: bold;
            color: #333;
            margin: 0;
            text-align: center;
        }

        .close-button {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #777;
            position: absolute;
            right: 0;
            top: -5px; /* 微调位置 */
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s ease;
        }

        .close-button:hover {
            background-color: #f5f5f5;
            color: #333;
        }

        .modal-body {
            margin-bottom: 28px;
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 90%; /* 控制内容区域宽度 */
        }

        .form-group {
            margin-bottom: 22px;
            width: 100%;
        }

        .form-group label {
            display: block;
            margin-bottom: 12px;
            font-weight: 500;
            color: #444;
            font-size: 16px;
            text-align: center; /* 标签居中 */
        }

        .form-control {
            width: 100%;
            padding: 14px 16px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 15px;
            transition: all 0.2s ease;
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
            display: block; /* 确保块级显示 */
            margin: 0 auto; /* 左右边距自动，实现居中 */
        }

        textarea.form-control {
            min-height: 150px;
            resize: vertical;
        }

        .form-control:focus {
            border-color: #4CAF50;
            box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
            outline: none;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            width: 100%;
            justify-content: center; /* 复选框居中 */
            padding: 8px 0;
            margin-top: 5px;
        }

        .checkbox-group input {
            margin-right: 10px;
            width: 18px;
            height: 18px;
        }

        .checkbox-group label {
            margin: 0;
            font-size: 16px;
            text-align: left; /* 复选框标签左对齐 */
            display: inline; /* 内联显示 */
        }

        .modal-footer {
            display: flex;
            justify-content: center;
            padding-top: 20px;
            border-top: 1px solid #eee;
            width: 100%; /* 确保底部栏占满宽度 */
        }

        .btn {
            padding: 12px 28px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 110px;
            text-align: center;
        }

        .btn-primary {
            background-color: #4CAF50;
            color: white;
            border: none;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .btn-secondary {
            background-color: #f5f5f5;
            color: #333;
            border: 1px solid #ddd;
            margin-right: 20px;
        }

        .btn:hover {
            opacity: 0.9;
            transform: translateY(-1px);
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
        }
        
        .btn:active {
            transform: translateY(1px);
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        /* 添加响应式支持 */
        @media screen and (max-width: 768px) {
            .filter-row {
                flex-direction: column;
                gap: 12px;
            }
            
            .filter-input {
                width: 100%;
            }
            
            #queryButton, #resetButton {
                width: 100%;
                margin-top: 8px;
            }
            
            .filter-buttons {
                flex-direction: column;
                width: 100%;
            }
            
            .pagination {
                flex-direction: column;
                align-items: center;
            }
            
            .page-info {
                margin-bottom: 12px;
                margin-right: 0;
            }

            .modal-content {
                padding: 24px 16px;
                width: 95%;
                max-width: 95%;
            }

            .btn {
                padding: 10px 20px;
                font-size: 15px;
                min-width: 90px;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="log-list-container">
            <h2>日志列表</h2>
            <!-- 优化后的过滤条件区块 -->
            <div class="filter-container">
                <div class="filter-row">
                    <input type="text" id="userIdFilter" class="filter-input" placeholder="用户ID">
                    <input type="text" id="userNameFilter" class="filter-input" placeholder="用户名称">
                    <input type="text" id="attachmentPathFilter" class="filter-input" placeholder="日志附件路径">
                </div>
                <div class="filter-row">
                    <select id="processStateFilter" class="filter-input filter-select">
                        <option value="all">全部</option>
                        <option value="0" selected >待处理</option>
                        <option value="1">已处理</option>
                    </select>
                    <input type="text" id="startTimeFilter" class="filter-input date-input" placeholder="开始时间" readonly>
                    <input type="text" id="endTimeFilter" class="filter-input date-input" placeholder="结束时间" readonly>
                </div>
                <div class="filter-row buttons-row">
                    <div class="filter-buttons">
                        <button id="queryButton">查询</button>
                        <button id="resetButton">重置</button>
                    </div>
                </div>
            </div>
            <table id="logTable">
                <thead>
                    <tr>
                        <th class="log-table-header">问题ID</th>
                        <th class="log-table-header">用户ID</th>
                        <th class="log-table-header">用户名称</th>
                        <th class="log-table-header">问题描述</th>
                        <th class="log-table-header">插件版本</th>
                        <th class="log-table-header">IDE类型</th>
                        <th class="log-table-header">日志附件地址</th>
                        <th class="log-table-header">上报时间</th>
                        <th class="log-table-header">问题分类</th>
                        <th class="log-table-header">处理状态</th>
                        <th class="log-table-header">处理结果</th>
                        <th class="log-table-header operation-column">操作</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- 动态生成表格内容 -->
                </tbody>
            </table>

            <!-- 分页区块移动到表格下方 -->
            <div class="pagination">
                <span class="page-info">第 1 页，共 10 页</span>
                <div class="page-controls">
                    <button id="prevPage" disabled>上一页</button>
                    <span class="page-number">1</span>
                    <button id="nextPage">下一页</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 优化后的处理结果弹框 -->
    <div id="processResultModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">更新处理结果</h3>
                <button class="close-button" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="logIdInput">
                <input type="hidden" id="attachmentPathInput"> <!-- 新增：附件地址输入框 -->
                <div class="form-group">
                    <label for="processResultInput">处理结果</label>
                    <textarea id="processResultInput" class="form-control" placeholder="请输入问题分析结果"></textarea>
                </div>
                <div class="form-group checkbox-group">
                    <input type="checkbox" id="processStateCheckbox">
                    <label for="processStateCheckbox">标记为已处理</label>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal()">取消</button>
                <button class="btn btn-primary" onclick="submitProcessResult()">提交</button>
            </div>
        </div>
    </div>

    <!-- 引入flatpickr JS -->
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/zh.js"></script>
    <script>
document.addEventListener("DOMContentLoaded", function () {
    const tableBody = document.querySelector("#logTable tbody");
    const prevPageButton = document.getElementById("prevPage");
    const nextPageButton = document.getElementById("nextPage");
    const pageNumberSpan = document.querySelector(".page-number");
    const pageInfoSpan = document.querySelector(".page-info");
    const userIdFilter = document.getElementById("userIdFilter");
    const userNameFilter = document.getElementById("userNameFilter");
    const attachmentPathFilter = document.getElementById("attachmentPathFilter");
    const processStateFilter = document.getElementById("processStateFilter");
    const startTimeFilter = document.getElementById("startTimeFilter");
    const endTimeFilter = document.getElementById("endTimeFilter");
    const processResultModal = document.getElementById("processResultModal");
    const resetButton = document.getElementById("resetButton");

    // 初始化时间选择器
    const dateTimeConfig = {
        enableTime: true,  // 启用时间选择
        dateFormat: "Y-m-d H:i:S",  // 设置日期时间格式为 yyyy-MM-dd HH:mm:ss
        time_24hr: true,  // 使用24小时制
        locale: "zh",  // 使用中文
        minuteIncrement: 1,  // 分钟增量
        secondIncrement: 1,  // 秒增量
        allowInput: true,  // 允许手动输入
        disableMobile: false, // 在移动设备上也使用flatpickr而不是原生控件
        position: "auto", // 自动定位
        static: false, // 让日历可以溢出容器
        onClose: function(selectedDates, dateStr, instance) {
            // 当日期选择器关闭时，自动聚焦到下一个输入框
            if (instance.element.id === "startTimeFilter" && !endTimeFilter.value) {
                setTimeout(() => {
                    endTimePickr.open();
                }, 100);
            }
        }
    };

    // 创建开始时间和结束时间选择器
    const startTimePickr = flatpickr(startTimeFilter, {
        ...dateTimeConfig,
        placeholder: "开始时间",
    });

    const endTimePickr = flatpickr(endTimeFilter, {
        ...dateTimeConfig,
        placeholder: "结束时间",
    });

    let currentPage = 1;
    const pageSize = 20;
    let totalPages = 1;

    // 重置按钮点击事件
    resetButton.addEventListener("click", function () {
        // 清空所有过滤项
        userIdFilter.value = "";
        userNameFilter.value = "";
        attachmentPathFilter.value = "";
        processStateFilter.value = "0";
        
        // 清空日期选择器
        startTimePickr.clear();
        endTimePickr.clear();
        
        // 重新加载列表数据（不带过滤条件）
        currentPage = 1;
        loadLogList();
    });

    // 查询按钮点击事件
    document.getElementById("queryButton").addEventListener("click", function () {
        const filters = {
            userId: userIdFilter.value,
            userName: userNameFilter.value,
            attachmentPath: attachmentPathFilter.value,
            processState: processStateFilter.value,
            startTime: startTimeFilter.value,
            endTime: endTimeFilter.value
        };
        currentPage = 1; // 重置为第一页
        loadLogList(currentPage, filters);
    });

    // 加载日志列表数据
    function loadLogList(page = 1, filters = {}) {
        let queryParams = `page=${page}&pageSize=${pageSize}`;
        for (const key in filters) {
            if (filters[key]) {
                queryParams += `&${key}=${encodeURIComponent(filters[key])}`;
            }
        }
        fetch(`/api/get_log_list?${queryParams}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    renderLogList(data.logList);
                    totalPages = Math.ceil(data.totalCount / pageSize);
                    updatePaginationControls();
                } else {
                    console.error("加载日志列表失败");
                }
            });
    }

    // 渲染日志列表
    function renderLogList(logList) {
        tableBody.innerHTML = ""; // 清空表格内容
        logList.forEach((log, index) => {
            const row = document.createElement("tr");
            row.innerHTML = `
                <td>${log.id}</td>
                <td>${log.userId}</td>
                <td>${log.userName}</td>
                <td>${log.description}</td>
                <td>${log.pluginVersion}</td>
                <td>${log.ideType}</td>
                <td>${log.attachmentPath}</td>
                <td>${log.gmtCreate}</td>
                <td>
                    <select onchange="updateLogType('${log.id}', this.value)" class="log-type-select">
                        <option value=""${!log.type ? ' selected' : ''}>请选择</option>
                        <option value="性能"${log.type === '性能' ? ' selected' : ''}>性能</option>
                        <option value="交互"${log.type === '交互' ? ' selected' : ''}>交互</option>
                        <option value="效果"${log.type === '效果' ? ' selected' : ''}>效果</option>
                    </select>
                </td>
                <td><span class="status-pill ${log.processState === 1 ? 'status-processed' : 'status-pending'}">${log.processState === 1 ? '已处理' : '待处理'}</span></td>
                <td>${log.processResult || '-'}</td>
                <td>
                    <div class="operation-buttons">
                        <button onclick="viewLog('${log.attachmentPath}')">查看日志详情</button>
                        <button onclick="openProcessModal('${log.id}', '${log.processResult || ''}', '${log.processState}', '${log.attachmentPath}')">更新处理结果</button>
                    </div>
                </td>
            `;
            tableBody.appendChild(row);
        });
    }

    // 查看日志详情
    window.viewLog = function (attachmentPath) {
        window.open(`/log_analysis?ossLink=${encodeURIComponent(attachmentPath)}`, '_blank');
    };

    // 上一页按钮点击事件
    prevPageButton.addEventListener("click", function () {
        if (currentPage > 1) {
            currentPage--;
            loadLogList(currentPage, getFilters());
        }
    });

    // 下一页按钮点击事件
    nextPageButton.addEventListener("click", function () {
        if (currentPage < totalPages) {
            currentPage++;
            loadLogList(currentPage, getFilters());
        }
    });

    // 获取当前过滤条件
    function getFilters() {
        return {
            userId: userIdFilter.value,
            userName: userNameFilter.value,
            attachmentPath: attachmentPathFilter.value,
            processState: processStateFilter.value,
            startTime: startTimeFilter.value,
            endTime: endTimeFilter.value
        };
    }

    // 更新分页控件
    function updatePaginationControls() {
        prevPageButton.disabled = currentPage === 1;
        nextPageButton.disabled = currentPage === totalPages;
        pageNumberSpan.textContent = currentPage;
        pageInfoSpan.textContent = `第 ${currentPage} 页，共 ${totalPages} 页`;
    }

    // 初始化加载
    loadLogList();
});

// 新增：打开处理结果弹框时，记录附件地址
window.openProcessModal = function(logId, processResult, processState, attachmentPath) {
    document.getElementById("logIdInput").value = logId;
    document.getElementById("processResultInput").value = processResult;
    document.getElementById("processStateCheckbox").checked = processState === '1';
    document.getElementById("attachmentPathInput").value = attachmentPath; // 新增：记录附件地址
    
    const modal = document.getElementById("processResultModal");
    modal.style.display = "flex";
};

// 新增：关闭弹框
window.closeModal = function() {
    const modal = document.getElementById("processResultModal");
    modal.style.display = "none";
};

// 新增：提交处理结果
window.submitProcessResult = function() {
    const logId = document.getElementById("logIdInput").value;
    const processResult = document.getElementById("processResultInput").value;
    const processState = document.getElementById("processStateCheckbox").checked ? '1' : '0';
    const attachmentPath = document.getElementById("attachmentPathInput").value; // 新增：获取附件地址
    
    // 发送数据到后端
    fetch('/api/update_log_process', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            logId: logId,
            processResult: processResult,
            processState: processState,
            attachmentPath: attachmentPath // 新增：传递附件地址
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('更新成功！');
            closeModal();
            // 重新加载当前页数据
            const queryButton = document.getElementById("queryButton");
            queryButton.click();
        } else {
            alert('更新失败：' + data.message);
        }
    })
    .catch(error => {
        alert('发生错误：' + error);
    });
};

// 点击弹框外部关闭弹框
window.addEventListener('click', function(event) {
    const modal = document.getElementById("processResultModal");
    if (event.target === modal) {
        closeModal();
    }
});

// 更新日志类型
window.updateLogType = function(logId, type) {
    if (!type) {
        return; // 如果选择空值，不进行更新
    }
    
    fetch('/api/update_log_process', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            logId: logId,
            type: type
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('问题分类更新成功');
        } else {
            alert('更新失败：' + data.message);
            // 失败时重新加载数据，恢复原状态
            location.reload();
        }
    })
    .catch(error => {
        alert('发生错误：' + error);
        // 出错时重新加载数据，恢复原状态
        location.reload();
    });
};
    </script>
</body>
</html>