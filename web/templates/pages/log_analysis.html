<!DOCTYPE html>
<html lang="utf8">
<head>
    <title>{{.title}}</title>
    <link rel="stylesheet" href="/static/css/style.css?123">
</head>
<body>
    <div class="main-container">
        <div class="log-analysis-container">
            <h2>日志分析</h2>
            <div class="search-section">
                <select id="logType" class="log-type-select">
                    <option value="go">Go端日志</option>
                    <option value="plugin">插件端日志</option>
                </select>
                <input type="text" id="logId" placeholder="请输入日志ID">
                <button id="searchButton" class="disabled-button">分析OSS日志</button>
                <!-- 新增文件上传按钮 -->
                <input type="file" id="logFileUpload" accept=".log,.bin" style="display: none;">
                <label for="logFileUpload" class="upload-label">上传diagnosis.bin文件</label>
                <!-- 将下载按钮移动到这里 -->
                <button id="downloadButton" class="upload-label">导出日志</button>
            </div>
            <div class="filter-section">
                <input type="text" id="filterKeyword" placeholder="请输入过滤关键词">
                <span id="filterStatus"></span>
            </div>
            <!-- 新增文件路径信息展示 -->
            <div id="filePathInfo" class="file-path-info"></div>
            <div id="logContent" class="log-content">
            </div>
        </div>
    </div>
    <script>
        // 解析URL参数并回写ossLink值到日志ID输入框
        document.addEventListener('DOMContentLoaded', function () {
            const urlParams = new URLSearchParams(window.location.search);
            const ossLink = urlParams.get('ossLink');
            if (ossLink) {
                document.getElementById('logId').value = ossLink;
            }
        });
    </script>
    <script src="/static/js/log_analysis.js?11"></script>
</body>
</html>