package service

import (
	"bytes"
	"dolphin/internal/model"
	"dolphin/pkg/config"
	"dolphin/pkg/encrypt"
	"dolphin/pkg/file"
	"dolphin/pkg/httpclient"
	"dolphin/pkg/ossutil"
	"fmt"
	"github.com/goccy/go-json"
	"log"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/robfig/cron/v3"
)

const _goLogFileName = "diagnosis.bin"
const _vscLogFileName = "vsc.log"
const _ideaLogFileName = "idea.log"
const LIST_ISSUE_API = "/api/system/v1/feedback/issues"
const UPDATE_ISSUE_API = "/api/system/v1/feedback/issues/"

type LogService struct {
}

func NewLogService() *LogService {
	return &LogService{}
}

func (s *LogService) ListLogs(page int, pageSize int, filters map[string]string) model.DiagnoseLogResponse {
	httpClient := httpclient.NewHttpClient()
	requestUrl := config.GetString("lingma-server.host") + LIST_ISSUE_API

	// 添加分页参数到URL
	params := url.Values{}
	params.Add("page", strconv.Itoa(page))
	params.Add("pageSize", strconv.Itoa(pageSize))

	// 添加过滤条件
	for key, value := range filters {
		if value != "" {
			params.Add(key, value)
		}
	}

	requestUrl += "?" + params.Encode()

	content, err := httpClient.Get(requestUrl)
	if err != nil {
		return model.DiagnoseLogResponse{}
	}
	// 将 JSON 数据解析为 DiagnoseLogResponse
	var response model.DiagnoseLogResponse
	err = json.Unmarshal(content, &response)
	if err != nil {
		// 如果解析失败，返回空的 DiagnoseLog 切片
		return model.DiagnoseLogResponse{}
	}
	return response
}

// ReadLog 读取日志
func (s *LogService) ReadLog(filePath string, logType string) ([]string, string) {
	var logStorageDir = config.GetString("log.storage.dir")
	// 例如：/Users/<USER>/tmp/cloud/0bbda138-f556-48b1-8d9a-fd2e03b9af62-1737798454
	localDir := logStorageDir + "/" + strings.TrimSuffix(filePath, ".zip")
	// 例如：/Users/<USER>/tmp/cloud/0bbda138-f556-48b1-8d9a-fd2e03b9af62-1737798454.zip
	logZipFilePath := localDir + ".zip"
	// 例如：/Users/<USER>/tmp/cloud/0bbda138-f556-48b1-8d9a-fd2e03b9af62-1737798454/diagnosis.bin
	goLogPath := localDir + "/diagnosis.bin"
	//如果压缩包文件不存在，则下载压缩包文件
	if !file.IsFileExists(logZipFilePath) {
		content, err := downloadFromOss(filePath)
		if err != nil {
			return nil, ""
		}
		// 创建目录
		dir := filepath.Dir(logZipFilePath)
		if err := os.MkdirAll(dir, 0755); err != nil {
			return nil, ""
		}
		// 将content内容写入到指定目录
		err = os.WriteFile(logZipFilePath, content, 0644)
		if err != nil {
			return nil, ""
		}
	}
	if !file.IsFileExists(logZipFilePath) {
		return []string{}, ""
	}
	//解压文件
	if !file.IsFileExists(goLogPath) {
		// 解压文件
		err := file.Unzip(logZipFilePath, localDir)
		if err != nil {
			return nil, ""
		}
		// 处理解压后的文件名
		entries, err := os.ReadDir(localDir)
		if err != nil {
			return nil, ""
		}
		for _, entry := range entries {
			fileName := entry.Name()
			newFileName := fileName
			if strings.Contains(fileName, "diagnosis.bin") {
				// 修改逻辑：保留从开始到diagnosis.bin结尾的部分
				newFileName = strings.Split(fileName, _goLogFileName)[0] + _goLogFileName
			} else if strings.Contains(fileName, ".log") {
				newFileName = strings.Split(fileName, ".log")[0] + ".log"
			}
			if newFileName != fileName {
				err := os.Rename(filepath.Join(localDir, fileName), filepath.Join(localDir, newFileName))
				if err != nil {
					return nil, ""
				}
			}
		}
	}
	// 按日志类型读取日志内容
	if logType == "go" {
		return encrypt.Decode(goLogPath), goLogPath
	} else if logType == "plugin" {
		return readIdeLog(localDir)
	} else {
		return nil, ""
	}
}

// 显示文件路径
func readIdeLog(logFileDir string) ([]string, string) {
	vscLogPath := logFileDir + "/" + _vscLogFileName
	ideaLogPath := logFileDir + "/" + _ideaLogFileName
	logPath := ""
	if file.IsFileExists(vscLogPath) {
		logPath = vscLogPath
	} else if file.IsFileExists(ideaLogPath) {
		logPath = ideaLogPath
	}
	if logPath == "" {
		return []string{}, ""
	}
	// 读取文件
	content, err := os.ReadFile(logPath)
	if err != nil {
		log.Fatal(err)
	}
	text := string(content)
	return strings.Split(text, "\n"), logPath
}

func downloadFromOss(filePath string) ([]byte, error) {
	content, err := ossutil.ReadFileContent(config.GetString("log.oss.bucket"), filePath)
	if err != nil {
		return []byte(""), nil
	}
	return content, err
}

func (s *LogService) UpdateLogProcess(logId, processResult, processState string) (bool, string) {
	// 构造请求URL
	requestUrl := config.GetString("lingma-server.host") + UPDATE_ISSUE_API + logId

	// 构造请求体
	requestBody := map[string]string{
		"id":            logId,
		"processResult": processResult,
		"processState":  processState,
	}

	// 发送HTTP请求
	httpClient := httpclient.NewHttpClient()
	content, err := httpClient.Post(requestUrl, requestBody)
	if err != nil {
		return false, "请求后端服务失败"
	}
	res := string(content)
	print(res)
	// 解析响应
	var response map[string]interface{}
	err = json.Unmarshal(content, &response)
	if err != nil {
		return false, "解析响应失败"
	}

	success, ok := response["success"].(bool)
	if !ok || !success {
		message, _ := response["message"].(string)
		return false, message
	}

	return true, ""
}

// DeleteAttachment 删除指定文件及其所在目录（除去扩展名后）下的所有文件
func (s *LogService) DeleteAttachment(attachmentPath string) error {
	// 获取文件目录
	dir := filepath.Dir(attachmentPath)
	// 获取文件名（不含扩展名）
	fileName := strings.TrimSuffix(filepath.Base(attachmentPath), filepath.Ext(attachmentPath))
	// 构建目标目录路径
	targetDir := filepath.Join(dir, fileName)

	// 删除指定文件
	if err := file.RemoveFile(attachmentPath); err != nil {
		return err
	}
	// 删除目标目录及其所有内容
	if err := file.RemoveFile(targetDir); err != nil {
		return err
	}
	return nil
}

// StartPeriodicLogCheck 启动定时任务，每10分钟执行一次，查询最近10分钟的日志
func (s *LogService) StartPeriodicLogCheck() {
	cronExpress := config.GetString("timer.cron")
	c := cron.New()
	_, err := c.AddFunc(cronExpress, func() { // 每天早上10点执行
		s.checkRecentLogs()
	})
	if err != nil {
		log.Fatalf("Failed to add cron job: %v", err)
	}
	c.Start()
	log.Println("定时日志检查任务已启动，每天早上10点执行一次")
}

// checkRecentLogs 检查最近10分钟的日志
func (s *LogService) checkRecentLogs() {
	// 获取当前时间并减去8小时
	endTime := time.Now()
	startTime := endTime.Add(-24 * time.Hour) //查询前一天的数据

	// 将时间转换为API所需的格式
	startTimeStr := startTime.Format("2006-01-02 15:04:05")
	endTimeStr := endTime.Format("2006-01-02 15:04:05")

	filters := map[string]string{
		"startTime": startTimeStr,
		"endTime":   endTimeStr,
	}

	// 调用现有的ListLogs方法，获取最近10分钟的日志
	// 页码从1开始，每页大小设为100，可以根据实际需要调整
	result := s.ListLogs(1, 100, filters)

	// 处理获取到的日志数据
	logCount := result.TotalCount
	log.Printf("获取到最近10分钟内的日志数量: %d", logCount)

	// 如果有新日志，发送钉钉通知
	if logCount > 0 {
		s.sendDingTalkNotification(logCount)
	}
}

// sendDingTalkNotification 发送钉钉通知
func (s *LogService) sendDingTalkNotification(logCount int) {
	// 从配置中获取钉钉webhook URL
	webhookURL := config.GetString("dingtalk.webhook")
	if webhookURL == "" {
		log.Println("未配置钉钉webhook URL，无法发送通知")
		return
	}

	// 构建通知内容
	content := fmt.Sprintf("接收到%d条用户上报问题", logCount)

	// 构建钉钉消息内容（使用text类型消息）
	message := map[string]interface{}{
		"msgtype": "text",
		"text": map[string]string{
			"content": content,
		},
	}

	// 将消息转换为JSON
	jsonMessage, err := json.Marshal(message)
	if err != nil {
		log.Printf("钉钉消息序列化失败: %v", err)
		return
	}

	// 发送HTTP请求
	resp, err := http.Post(webhookURL, "application/json", bytes.NewBuffer(jsonMessage))
	if err != nil {
		log.Printf("发送钉钉通知失败: %v", err)
		return
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		log.Printf("钉钉通知发送失败，状态码: %d", resp.StatusCode)
		return
	}

	log.Printf("成功发送钉钉通知: %s", content)
}
