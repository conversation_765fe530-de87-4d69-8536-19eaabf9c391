package handler

import (
	"dolphin/internal/model"
	"dolphin/pkg/config"
	"github.com/gin-contrib/sessions"
	"github.com/gin-gonic/gin"
)

type AuthHandler struct{}

func NewAuthHandler() *AuthHandler {
	return &AuthHandler{}
}

func (h *AuthHandler) LoginPage(c *gin.Context) {
	c.HTML(200, "login.html", gin.H{
		"title": "登录",
	})
}

func (h *AuthHandler) Login(c *gin.Context) {
	username := c.PostForm("username")
	password := c.PostForm("password")
	// 这里简化了验证逻辑，实际应用中应该查询数据库
	var login model.User
	config.GetObject("login", &login)
	if username == login.Username && password == login.Password {
		session := sessions.Default(c)
		session.Set("user", username)
		session.Save()
		c.<PERSON>(200, gin.H{"success": true, "redirect": "/log-list"})
	} else {
		c.<PERSON>(400, gin.H{"success": false, "message": "用户名或密码错误"})
	}
}
