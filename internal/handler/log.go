package handler

import (
	"dolphin/internal/model"
	"dolphin/internal/service"
	"dolphin/pkg/config"
	"dolphin/pkg/encrypt"
	"dolphin/pkg/file"
	"fmt"
	"github.com/gin-gonic/gin"
	"log"
	"os"
	"path/filepath"
	"strconv"
	"strings"
)

type LogHandler struct {
	LogService *service.LogService
}

func NewLogHandler(logService *service.LogService) *LogHandler {
	return &LogHandler{logService}
}

func (h *LogHandler) LogListPage(c *gin.Context) {
	c.HTML(200, "log-list.html", gin.H{
		"title": "日志列表",
	})
}

func (h *LogHandler) LogAnalysisPage(c *gin.Context) {
	c.HTML(200, "log_analysis.html", gin.H{
		"title": "日志列表",
	})
}

func (h *LogHandler) AnalysisLog(c *gin.Context) {
	logId := c.<PERSON>Form("logId")
	logType := c.PostForm("logType") // 获取日志类型参数

	// 这里应该实现实际的日志查询逻辑
	logData, path := h.LogService.ReadLog(logId, logType) // 将logType传递给服务层
	c.JSON(200, gin.H{"success": true, "logLines": logData, "filePath": path})
}

func (h *LogHandler) GetLogList(c *gin.Context) {
	var result model.DiagnoseLogResponse

	// 获取分页参数
	page, err := strconv.Atoi(c.Query("page"))
	if err != nil || page < 1 {
		page = 1
	}
	pageSize, err := strconv.Atoi(c.Query("pageSize"))
	if err != nil || pageSize < 1 {
		pageSize = 20 // 默认每页显示20行记录
	}
	processState := c.Query("processState")
	if processState == "" {
		processState = "0"
	} else if processState == "all" {
		processState = ""
	}
	// 获取过滤条件
	filters := map[string]string{
		"userId":         c.Query("userId"),
		"userName":       c.Query("userName"),
		"attachmentPath": c.Query("attachmentPath"),
		"processState":   processState,
		"startTime":      c.Query("startTime"),
		"endTime":        c.Query("endTime"),
	}

	// 调用服务层方法并传递分页和过滤条件
	result = h.LogService.ListLogs(page, pageSize, filters)

	c.JSON(200, gin.H{"success": true, "totalCount": result.TotalCount, "logList": result.Data})
}

func (h *LogHandler) UploadLogFile(c *gin.Context) {
	uploadFile, err := c.FormFile("logFile")
	if err != nil {
		c.JSON(400, gin.H{"success": false, "message": "文件上传失败"})
		return
	}
	logFileDir := config.GetString("log.storage.dir")

	// 计算文件的MD5值
	md5Hash, err := file.GetMD5(uploadFile)
	if err != nil {
		c.JSON(500, gin.H{"success": false, "message": "计算文件MD5值失败"})
		return
	}

	// 生成新的文件名
	newFileName := fmt.Sprintf("%s_%s", uploadFile.Filename, md5Hash)
	newFilePath := filepath.Join(logFileDir, newFileName)

	// 检查文件是否存在
	if !file.IsFileExists(newFilePath) {
		// 保存文件
		err = c.SaveUploadedFile(uploadFile, newFilePath)
		if err != nil {
			c.JSON(500, gin.H{"success": false, "message": "文件保存失败"})
			return
		}
	}

	// 假设文件内容是纯文本日志，按行分割
	logLines := encrypt.Decode(newFilePath)
	c.JSON(200, gin.H{"success": true, "logLines": logLines, "filePath": newFilePath})
}

func (h *LogHandler) DownloadFile(c *gin.Context) {
	filePath := c.Query("filePath")
	if filePath == "" {
		c.JSON(400, gin.H{"success": false, "message": "文件路径不能为空"})
		return
	}

	// 检查文件是否存在
	if !file.IsFileExists(filePath) {
		c.JSON(404, gin.H{"success": false, "message": "文件不存在"})
		return
	}
	// diagnose.bin文件需要
	fileName := filepath.Base(filePath)
	if strings.Contains(fileName, ".bin") {
		fileName = "lingma.log"
	}
	// 设置响应头以触发文件下载
	c.Header("Content-Disposition", "attachment; filename="+fileName)
	c.Header("Content-Type", "application/octet-stream")

	var fileContent string
	// 解密文件内容
	if fileName == "lingma.log" {
		logLines := encrypt.Decode(filePath)
		fileContent = strings.Join(logLines, "\n")
	} else {
		content, err := os.ReadFile(filePath)
		if err != nil {
			log.Fatal(err)
		}
		fileContent = string(content)
	}

	// 将解密后的内容写入响应体
	_, err := c.Writer.Write([]byte(fileContent))
	if err != nil {
		c.JSON(500, gin.H{"success": false, "message": "文件下载失败"})
		return
	}
}

func (h *LogHandler) UpdateLogProcess(c *gin.Context) {
	var req struct {
		LogId          string `json:"logId"`
		ProcessResult  string `json:"processResult"`
		ProcessState   string `json:"processState"`
		AttachmentPath string `json:"attachmentPath"`
		Type           string `json:"type"`
	}

	// 解析请求体
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{"success": false, "message": "参数解析失败"})
		return
	}

	// 调用服务层方法更新日志处理状态
	success, errMsg := h.LogService.UpdateLogProcess(req.LogId, req.ProcessResult, req.ProcessState)
	if !success {
		c.JSON(500, gin.H{"success": false, "message": errMsg})
		return
	}
	if req.ProcessState == "1" {
		localPath := config.GetString("log.storage.dir") + "/" + req.AttachmentPath
		err := h.LogService.DeleteAttachment(localPath)
		if err != nil {
			c.JSON(500, gin.H{"success": false, "message": nil})
			return
		}
	}
	c.JSON(200, gin.H{"success": true, "message": "更新成功"})
}
