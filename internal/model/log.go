package model

type DiagnoseLog struct {
	Id             int64  `json:"id"`
	UserId         string `json:"userId"`
	UserName       string `json:"userName"`
	GmtCreate      string `json:"gmtCreate"`
	GmtModified    string `json:"gmtModified"`
	Description    string `json:"description"`
	ProcessState   int    `json:"processState"`
	ProcessResult  string `json:"processResult"`
	Processor      string `json:"processor"`
	PluginVersion  string `json:"pluginVersion"`
	IdeType        string `json:"ideType"`
	AttachmentPath string `json:"attachmentPath"`
	Type           string `json:"type"`
}

type DiagnoseLogResponse struct {
	Success    bool          `json:"success"`
	Data       []DiagnoseLog `json:"result"`
	PageSize   int           `json:"pageSize"`
	ToPage     int           `json:"toPage"`
	TotalCount int           `json:"totalCount"`
	TotalPages int           `json:"totalPages"`
}
